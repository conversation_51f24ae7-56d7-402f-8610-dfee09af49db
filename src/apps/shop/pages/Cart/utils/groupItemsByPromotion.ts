import { CartItemType } from '@/libs/cart/types';
import { PromoType } from '@/types/common';
import { PromotionGroup } from '../components/CartVendorPromoItem/CartVendorPromoItem';

/**
 * Groups cart items by their promotions and creates PromotionGroup objects
 * that contain all promotion data plus the array of items belonging to that promotion
 */
export function groupItemsByPromotion(
  items: CartItemType[],
  promotionType: string,
): PromotionGroup[] {
  // Create a map to group items by promotion ID
  const promotionMap = new Map<
    string,
    {
      promotion: PromoType;
      items: CartItemType[];
    }
  >();

  // Filter and group items by promotion type
  items.forEach((item) => {
    const promotions = item.product.promotions || [];

    // Find promotion of the specified type
    const targetPromo = promotions.find(
      (promo) => promo.type === promotionType,
    );

    if (targetPromo) {
      const promoId = targetPromo.id;

      if (!promotionMap.has(promoId)) {
        promotionMap.set(promoId, {
          promotion: targetPromo,
          items: [],
        });
      }

      promotionMap.get(promoId)!.items.push(item);
    }
  });

  // Convert map to PromotionGroup array with calculated totals
  return Array.from(promotionMap.values()).map(({ promotion, items }) => {
    const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);
    const totalItems = items.length;

    // Calculate original total price (sum of all item subtotals)
    const originalTotalPrice = items.reduce((sum, item) => {
      const subtotal = parseFloat(item.subtotal || '0');
      return sum + subtotal;
    }, 0);

    // Calculate discounted price based on promotion logic
    // This is a simplified calculation - you might need to adjust based on your promotion logic
    let discountedTotalPrice = originalTotalPrice;
    let totalSavings = 0;

    // For buy_x_get_y promotions, calculate the savings
    if (promotion.type === 'buy_x_get_y') {
      items.forEach((item) => {
        const offer = item.product.offers.find(
          (o) => o.id === item.productOfferId,
        );
        if (offer) {
          const unitPrice = parseFloat(offer.clinicPrice || offer.price || '0');

          // Get promotion requirements and benefits
          const minReq = promotion.requirements?.find(
            (req) => req.type === 'minimum_quantity',
          );
          const freeBenefit = promotion.benefits?.find(
            (benefit) => benefit.type === 'give_free_product',
          );

          if (minReq && freeBenefit) {
            const minQty = parseInt(minReq.minimumQuantity || '0');
            const freeQtyPerTrigger = parseInt(freeBenefit.quantity || '0');

            if (minQty > 0) {
              const triggers = Math.floor(item.quantity / minQty);
              const freeQty = triggers * freeQtyPerTrigger;
              const savings = freeQty * unitPrice;

              totalSavings += savings;
            }
          }
        }
      });

      discountedTotalPrice = originalTotalPrice - totalSavings;
    }

    return {
      promotion,
      items,
      totalItems,
      totalQuantity,
      originalTotalPrice,
      discountedTotalPrice,
      totalSavings,
    };
  });
}

/**
 * Filters cart items to get only those that have promotions of a specific type
 */
export function getItemsWithPromotions(
  items: CartItemType[],
  promotionType: string,
): CartItemType[] {
  return items.filter((item) => {
    const promotions = item.product.promotions || [];
    return promotions.some((promo) => promo.type === promotionType);
  });
}

/**
 * Filters cart items to get only those without promotions
 */
export function getItemsWithoutPromotions(
  items: CartItemType[],
): CartItemType[] {
  return items.filter((item) => {
    const promotions = item.product.promotions || [];
    return promotions.length === 0;
  });
}
