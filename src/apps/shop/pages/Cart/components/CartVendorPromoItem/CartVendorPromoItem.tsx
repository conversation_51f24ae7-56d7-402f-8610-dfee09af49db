type CartVendorPromoItemProps = {
  promotion: {
    imageUrl: string;
    name: string;
    vendorSku: string;
    manufacturer: string | null;
  };
};

export const CartVendorPromoItem = ({
  promotion,
}: CartVendorPromoItemProps) => {
  return (
    <div className="flex w-full rounded-lg p-4">
      <div className="relative h-32 w-60 overflow-hidden rounded-lg border border-solid border-[#f2f2f2] bg-white p-0">
        <img
          src=""
          alt=""
          className="absolute top-[calc(50%+1rem)] left-1/2 max-h-[calc(100%-2rem)] w-full max-w-[calc(100%-2rem)] -translate-x-1/2 -translate-y-1/2 object-scale-down data-[fallback=true]:top-1/2"
        />
      </div>
      <div className="flex-1">
        <h3 className="max-w-9/10 text-sm leading-5 font-medium text-black">
          {promotion?.name}
        </h3>
        <div className="m-1 mb-3 flex text-center">
          <span className="text-xs text-neutral-500/80">
            SKU:
            <span className="ml-0.5 text-xs font-medium text-[#333333]">
              {offer.vendorSku}
            </span>
          </span>
          {item.product.manufacturer ? (
            <>
              <Divider orientation="vertical" h="1rem" mx="md" />
              <Text ml="2px" c="#344054" size="xs">
                {item.product.manufacturer}
              </Text>
            </>
          ) : null}
        </div>
        <div className="divider-h"></div>
      </div>
    </div>
  );
};
