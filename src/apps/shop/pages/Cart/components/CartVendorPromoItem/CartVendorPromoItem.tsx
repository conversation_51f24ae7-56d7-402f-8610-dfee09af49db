import { CartItemType } from '@/libs/cart/types';
import { PromoType } from '@/types/common';
import { Divider, Text } from '@mantine/core';

export interface PromotionGroup {
  promotion: PromoType;
  items: CartItemType[];
  totalItems: number;
  totalQuantity: number;
  originalTotalPrice: number;
  discountedTotalPrice: number;
  totalSavings: number;
}

type CartVendorPromoItemProps = {
  promotionGroup: PromotionGroup;
};

export const CartVendorPromoItem = ({
  promotionGroup,
}: CartVendorPromoItemProps) => {
  const {
    promotion,
    items,
    totalItems,
    originalTotalPrice,
    discountedTotalPrice,
    totalSavings,
  } = promotionGroup;

  // Get the first item for display purposes (image, manufacturer, etc.)
  const firstItem = items[0];
  const firstProduct = firstItem?.product;

  return (
    <div className="flex w-full rounded-lg p-4">
      <div className="relative h-32 w-60 overflow-hidden rounded-lg border border-solid border-[#f2f2f2] bg-white p-0">
        <img
          src={firstProduct?.imageUrl || ''}
          alt={promotion.name}
          className="absolute top-[calc(50%+1rem)] left-1/2 max-h-[calc(100%-2rem)] w-full max-w-[calc(100%-2rem)] -translate-x-1/2 -translate-y-1/2 object-scale-down data-[fallback=true]:top-1/2"
        />
      </div>
      <div className="flex-1">
        <h3 className="max-w-9/10 text-sm leading-5 font-medium text-black">
          {promotion.name}
        </h3>
        <div className="m-1 mb-3 flex text-center">
          <span className="text-xs text-neutral-500/80">
            {totalItems} items in promotion
          </span>
          {firstProduct?.manufacturer ? (
            <>
              <Divider orientation="vertical" h="1rem" mx="md" />
              <Text ml="2px" c="#344054" size="xs">
                {firstProduct.manufacturer}
              </Text>
            </>
          ) : null}
        </div>
        <div className="divider-h"></div>

        {/* Display pricing information */}
        <div className="mt-2 mb-3">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-green-600">
              ${discountedTotalPrice.toFixed(2)}
            </span>
            {totalSavings > 0 && (
              <>
                <span className="text-xs text-neutral-500 line-through">
                  ${originalTotalPrice.toFixed(2)}
                </span>
                <span className="text-xs font-medium text-green-600">
                  Save ${totalSavings.toFixed(2)}
                </span>
              </>
            )}
          </div>
        </div>

        {/* Display individual items in the promotion */}
        <div className="mt-2">
          {items.map((item) => (
            <div key={item.id} className="mb-2 text-xs">
              <span className="font-medium">{item.product.name}</span>
              <span className="ml-2 text-neutral-500">
                Qty: {item.quantity}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
