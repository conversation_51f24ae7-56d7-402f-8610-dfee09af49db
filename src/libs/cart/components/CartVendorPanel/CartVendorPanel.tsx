import React from 'react';
import { Flex, Text, Divider, Image } from '@mantine/core';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import type { CartItemType, CartVendorType } from '@/libs/cart/types';
import { getPriceString } from '@/utils';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import CutoffIcon from '@/assets/images/cart/clock.svg?react';
import { getSubtotalItemsText } from '@/libs/cart/utils/getSubtotalItemsText';
import styles from './CartVendorPanel.module.css';
import clsx from 'clsx';
import { CartVendorProductItem } from '@/apps/shop/pages/Cart/components/CartVendorProductItem/CartVendorProductItem';
import { SavedItemsPanel } from './components/SavedItemsPanel/SavedItemsPanel';
import { FEATURE_FLAGS } from '@/constants';
import { CartVendorPromoItem } from '@/apps/shop/pages/Cart/components/CartVendorPromoItem/CartVendorPromoItem';

type CartVendorPanelProps = {
  vendor: CartVendorType;
};

export const CartVendorPanel = (props: CartVendorPanelProps) => {
  const {
    vendor: {
      imageUrl,
      name,
      shippingFee,
      subtotal,
      items,
      amountToFreeShipping,
      cutoffTime,
    },
  } = props;

  const isShippingFee = +shippingFee === 0;

  const buyXGetYItems = items.filter(
    (item) =>
      item.product.promotions &&
      item.product.promotions.some((promo) => promo.type === 'buy_x_get_y'),
  );
  const noPromoItems = items.filter(
    (item) =>
      !item.product.promotions ||
      !item.product.promotions.some((promo) => promo.type === 'buy_x_get_y'),
  );

  const { gpoItems, otherItems } = noPromoItems.reduce<{
    gpoItems: CartItemType[];
    otherItems: CartItemType[];
    // gpoSavings: number;
    // vendorSavings: number;
  }>(
    (acc, item: CartItemType) => {
      const offer = item.product.offers.find(
        ({ id }) => id === item.productOfferId,
      )!;
      const { isRecommended } = offer;
      // const { gpoSavings, vendorSavings } = getProductOfferComputedData(offer);

      if (isRecommended) {
        acc.gpoItems.push(item);
      } else {
        acc.otherItems.push(item);
      }

      // acc.gpoSavings += gpoSavings;
      // acc.vendorSavings += vendorSavings;

      return acc;
    },
    {
      gpoItems: [],
      otherItems: [],
      // gpoSavings: 0,
      // vendorSavings: 0,
    },
  );

  return (
    <CollapsiblePanel
      header={
        <Flex align="center" pr="5rem">
          <Image
            src={imageUrl}
            alt={name}
            fallbackSrc={defaultProductImgUrl}
            h={42}
            title={name}
          />

          <Flex align="center" gap="md" px="md">
            <Flex align="flex-end" lh="1">
              <Text fw="500" mr="0.4rem">
                {getPriceString(subtotal)}
              </Text>
              {/* <Text
                  fw="500"
                  size="0.75rem"
                  c="rgba(51, 51, 51, 0.30)"
                  td="line-through"
                >
                  {getPriceString(subtotal)}
                </Text> */}
            </Flex>

            <Divider orientation="vertical" />

            <Flex>
              <Text c="rgba(51, 51, 51, 0.50)" fw="400" size="sm">
                {getSubtotalItemsText(items.length)}
              </Text>
            </Flex>

            {+amountToFreeShipping > 0 || +shippingFee === 0 ? (
              <>
                <Divider orientation="vertical" />
                <div
                  className={clsx(styles.infoCell, {
                    [styles.warning]: !isShippingFee,
                  })}
                >
                  {isShippingFee ? (
                    <Text
                      fw="400"
                      fs="0.875rem"
                      bg="#CEEDC2"
                      c="#225A0F"
                      px=".5rem"
                      py=".25rem"
                      style={{ borderRadius: '6px' }}
                    >
                      Shipping free
                    </Text>
                  ) : (
                    <Text size="0.875rem">
                      Add{' '}
                      <Text span fw="500">
                        {amountToFreeShipping}
                      </Text>{' '}
                      and get{' '}
                      <Text span fw="500">
                        Free Shipping
                      </Text>
                    </Text>
                  )}
                </div>
              </>
            ) : null}

            {cutoffTime && (
              <>
                <Divider orientation="vertical" />
                <div className={styles.infoCell}>
                  <Flex align="center">
                    <CutoffIcon />
                    <Text ml="4" size="0.875rem">
                      {cutoffTime}
                    </Text>
                  </Flex>
                </div>
              </>
            )}
          </Flex>
        </Flex>
      }
      content={
        <>
          <div className={styles.productsList}>
            {gpoItems.map((cartItem) => (
              <div key={cartItem.productOfferId} className={styles.productItem}>
                <CartVendorProductItem data={cartItem} />
              </div>
            ))}
            {otherItems.map((cartItem) => (
              <div key={cartItem.productOfferId} className={styles.productItem}>
                <CartVendorProductItem data={cartItem} />
              </div>
            ))}
            {buyXGetYItems.map((cartItem) => (
              <div key={cartItem.productOfferId} className={styles.productItem}>
                <CartVendorPromoItem items={buyXGetYItems} />
              </div>
            ))}
          </div>
          {FEATURE_FLAGS.SAVED_ITEMS && (
            <div className="mb-4 p-4">
              <SavedItemsPanel />
            </div>
          )}
        </>
      }
      startOpen
    />
  );
};
